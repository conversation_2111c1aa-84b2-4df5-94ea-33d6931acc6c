#!/usr/bin/env python3
"""
简单的HTTP文件服务器
用于通过内网穿透提供图片访问服务
"""

import http.server
import socketserver
import os
import sys
import threading
import time
from pathlib import Path
import mimetypes

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""
    
    def __init__(self, *args, directory=None, **kwargs):
        if directory is None:
            directory = os.getcwd()
        self.directory = directory
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        # 添加CORS头部，允许跨域访问
        self.send_response(200)
        
        # 获取文件路径
        path = self.translate_path(self.path)
        
        if os.path.isfile(path):
            # 获取文件类型
            content_type, _ = mimetypes.guess_type(path)
            if content_type:
                self.send_header('Content-Type', content_type)
            
            # 添加CORS头部
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', '*')
            
            # 获取文件大小
            file_size = os.path.getsize(path)
            self.send_header('Content-Length', str(file_size))
            self.end_headers()
            
            # 发送文件内容
            with open(path, 'rb') as f:
                self.copyfile(f, self.wfile)
        else:
            self.send_error(404, "File not found")
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.end_headers()
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

class FileServer:
    """文件服务器类"""
    
    def __init__(self, port=5055, directory=None):
        self.port = port
        self.directory = directory or "images"
        self.server = None
        self.server_thread = None
        
        # 创建图片目录
        Path(self.directory).mkdir(exist_ok=True)
    
    def start(self):
        """启动服务器"""
        try:
            # 创建请求处理器
            handler = lambda *args, **kwargs: CustomHTTPRequestHandler(
                *args, directory=self.directory, **kwargs
            )
            
            # 创建服务器
            self.server = socketserver.TCPServer(("", self.port), handler)
            
            print(f"文件服务器启动成功!")
            print(f"本地地址: http://localhost:{self.port}")
            print(f"服务目录: {os.path.abspath(self.directory)}")
            print(f"按 Ctrl+C 停止服务器")
            
            # 在新线程中启动服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()
            
            return True
            
        except OSError as e:
            if e.errno == 10048:  # 端口被占用
                print(f"错误: 端口 {self.port} 已被占用")
                return False
            else:
                print(f"启动服务器失败: {e}")
                return False
        except Exception as e:
            print(f"启动服务器失败: {e}")
            return False
    
    def stop(self):
        """停止服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("文件服务器已停止")
    
    def add_image(self, image_path):
        """添加图片到服务器目录"""
        try:
            image_path = Path(image_path)
            if not image_path.exists():
                raise FileNotFoundError(f"图片文件不存在: {image_path}")
            
            # 生成唯一文件名
            import uuid
            file_extension = image_path.suffix
            unique_filename = f"{uuid.uuid4().hex}{file_extension}"
            
            # 复制文件到服务器目录
            dest_path = Path(self.directory) / unique_filename
            import shutil
            shutil.copy2(image_path, dest_path)
            
            # 返回访问URL
            return f"/{unique_filename}"
            
        except Exception as e:
            print(f"添加图片失败: {e}")
            return None
    
    def get_full_url(self, relative_path, external_base_url):
        """获取完整的外网访问URL"""
        return f"{external_base_url.rstrip('/')}{relative_path}"

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='简单的HTTP文件服务器')
    parser.add_argument('-p', '--port', type=int, default=5055, help='服务器端口 (默认: 5055)')
    parser.add_argument('-d', '--directory', default='images', help='服务目录 (默认: images)')
    
    args = parser.parse_args()
    
    # 创建并启动服务器
    server = FileServer(port=args.port, directory=args.directory)
    
    if server.start():
        try:
            # 保持服务器运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
            server.stop()
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
