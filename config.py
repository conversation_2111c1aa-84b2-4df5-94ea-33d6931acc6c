# 图像识别AI系统配置文件

# 文件服务器配置
SERVER_CONFIG = {
    # 本地服务器端口（需要与内网穿透配置一致）
    'port': 5055,
    
    # 图片存储目录
    'images_directory': 'images',
    
    # 外网访问地址（你的内网穿透地址）
    'external_url': 'http://kewk.hppro1.hpnu.cn',
}

# API配置
API_CONFIG = {
    # Gemini API地址
    'base_url': 'https://api.lolimi.cn/API/AI/gemini.php',
    
    # 请求超时时间（秒）
    'timeout': 30,
    
    # 默认问题
    'default_question': '这个图片是在说什么？',
}

# GUI配置
GUI_CONFIG = {
    # 窗口大小
    'window_size': '950x800',
    
    # 图片预览大小
    'preview_size': (350, 250),
    
    # 支持的图片格式
    'supported_formats': [
        ('图片文件', '*.jpg *.jpeg *.png *.gif *.bmp *.webp'),
        ('所有文件', '*.*')
    ],
}

# 内网穿透配置说明
TUNNEL_CONFIG_HELP = """
内网穿透配置说明：

1. 确保你的内网穿透服务正在运行
2. 本地端口应该设置为 {port}
3. 外网地址应该设置为你的穿透域名

当前配置：
- 本地端口: {port}
- 外网地址: {external_url}

如需修改，请编辑 config.py 文件中的 SERVER_CONFIG 部分。
""".format(
    port=SERVER_CONFIG['port'],
    external_url=SERVER_CONFIG['external_url']
)
