#!/usr/bin/env python3
"""
API测试工具
用于测试Gemini API的调用
"""

import requests
import json
import urllib.parse

def test_api():
    """测试API调用"""
    print("=" * 60)
    print("Gemini API 测试工具")
    print("=" * 60)
    
    # API配置
    base_url = "https://api.lolimi.cn/API/AI/gemini.php"
    
    # 测试用例
    test_cases = [
        {
            "name": "无图片测试",
            "params": {
                "msg": "你好，请介绍一下你自己",
                "img": ""
            }
        },
        {
            "name": "网络图片测试",
            "params": {
                "msg": "这个图片是什么？",
                "img": "https://via.placeholder.com/300x200.png?text=Test+Image"
            }
        },
        {
            "name": "无效图片URL测试",
            "params": {
                "msg": "这个图片是什么？",
                "img": "https://invalid-url.com/test.jpg"
            }
        }
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print("-" * 40)
        
        try:
            # 构建完整URL
            full_url = f"{base_url}?{urllib.parse.urlencode(test_case['params'])}"
            print(f"请求URL: {full_url}")
            
            # 发送请求
            response = requests.get(base_url, params=test_case['params'], headers=headers, timeout=30)
            
            print(f"状态码: {response.status_code}")
            print(f"内容类型: {response.headers.get('content-type', 'unknown')}")
            print(f"响应大小: {len(response.text)} 字符")
            
            # 检查响应内容
            response_text = response.text.strip()
            
            if response_text.startswith('<!DOCTYPE html>') or response_text.startswith('<html'):
                print("❌ 响应类型: HTML页面")
                print(f"HTML内容预览: {response_text[:200]}...")
            else:
                print("✅ 响应类型: 可能是JSON")
                try:
                    result = response.json()
                    print("✅ JSON解析成功")
                    print(f"响应数据: {json.dumps(result, ensure_ascii=False, indent=2)}")
                except json.JSONDecodeError as e:
                    print(f"❌ JSON解析失败: {e}")
                    print(f"原始响应: {response_text[:500]}")
            
        except requests.exceptions.RequestException as e:
            print(f"❌ 请求失败: {e}")
        except Exception as e:
            print(f"❌ 未知错误: {e}")
        
        print()
    
    print("=" * 60)
    print("测试完成")
    
    # 交互式测试
    print("\n是否进行交互式测试? (y/n): ", end="")
    if input().lower() in ['y', 'yes', '是']:
        interactive_test(base_url, headers)

def interactive_test(base_url, headers):
    """交互式测试"""
    print("\n" + "=" * 60)
    print("交互式API测试")
    print("=" * 60)
    print("输入 'quit' 退出")
    
    while True:
        print("\n请输入测试参数:")
        msg = input("问题内容: ").strip()
        if msg.lower() == 'quit':
            break
        
        img = input("图片URL (可留空): ").strip()
        
        params = {
            'msg': msg,
            'img': img
        }
        
        print(f"\n发送请求...")
        try:
            response = requests.get(base_url, params=params, headers=headers, timeout=30)
            print(f"状态码: {response.status_code}")
            
            response_text = response.text.strip()
            
            if response_text.startswith('<!DOCTYPE html>') or response_text.startswith('<html'):
                print("❌ 返回HTML页面")
                print(f"内容: {response_text[:300]}...")
            else:
                try:
                    result = response.json()
                    print("✅ 成功获取JSON响应:")
                    print(json.dumps(result, ensure_ascii=False, indent=2))
                except json.JSONDecodeError:
                    print("❌ 非JSON响应:")
                    print(response_text[:500])
                    
        except Exception as e:
            print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    test_api()
