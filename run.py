#!/usr/bin/env python3
"""
图像识别AI GUI应用启动脚本
"""

import sys
import subprocess
import importlib.util

def check_and_install_requirements():
    """检查并安装必要的依赖包"""
    required_packages = {
        'requests': 'requests',
        'PIL': 'Pillow'
    }
    
    missing_packages = []
    
    for package_name, pip_name in required_packages.items():
        if importlib.util.find_spec(package_name) is None:
            missing_packages.append(pip_name)
    
    if missing_packages:
        print("检测到缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        
        response = input("\n是否自动安装这些依赖包? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            try:
                for package in missing_packages:
                    print(f"正在安装 {package}...")
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print("所有依赖包安装完成!")
            except subprocess.CalledProcessError as e:
                print(f"安装失败: {e}")
                print("请手动运行: pip install -r requirements.txt")
                return False
        else:
            print("请手动安装依赖包: pip install -r requirements.txt")
            return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("图像识别AI GUI应用")
    print("=" * 50)
    
    # 检查依赖
    if not check_and_install_requirements():
        input("按回车键退出...")
        return
    
    # 启动应用
    try:
        print("正在启动应用...")
        from image_ai_gui import main as app_main
        app_main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保 image_ai_gui.py 文件存在")
    except Exception as e:
        print(f"启动失败: {e}")
    
    input("按回车键退出...")

if __name__ == "__main__":
    main()
