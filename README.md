# 图像识别AI GUI应用 - 集成版

基于Gemini API的图像识别桌面应用程序，集成了内网穿透文件服务器，提供完整的本地图片上传和AI分析解决方案。

## 🚀 功能特点

- 🖼️ **本地图片上传**: 自动将本地图片上传到内置文件服务器
- 🌐 **网络图片支持**: 支持直接使用网络图片URL
- 🔗 **内网穿透集成**: 通过内网穿透技术实现外网访问
- 💬 **智能对话**: 可以向AI提问关于图片的任何问题
- 🎨 **现代界面**: 简洁美观的GUI设计，实时状态显示
- ⚡ **实时预览**: 图片选择后立即预览
- 📊 **详细结果**: 显示完整的API响应和分析结果
- ⚙️ **配置化**: 支持通过配置文件自定义设置

## 📋 系统要求

- Python 3.7+
- tkinter (通常随Python安装)
- requests
- Pillow (PIL)
- 内网穿透服务（如frp、ngrok等）

## 🛠️ 快速开始

### 方法一：一键启动（推荐）
```bash
python start_system.py
```

### 方法二：手动安装
1. 安装依赖包：
```bash
pip install -r requirements.txt
```

2. 配置内网穿透（重要）：
   - 确保内网穿透服务正在运行
   - 本地端口设置为 5055
   - 记录你的外网访问地址

3. 修改配置文件 `config.py`：
```python
SERVER_CONFIG = {
    'port': 5055,  # 本地端口
    'external_url': 'http://你的外网地址',  # 修改为你的外网地址
}
```

4. 运行应用：
```bash
python image_ai_gui_integrated.py
```

## 📖 使用说明

### 1. 启动系统
运行 `python start_system.py`，系统会自动：
- 检查依赖包
- 启动文件服务器（端口5055）
- 打开GUI界面

### 2. 选择图片
- **本地图片**: 点击"📁 选择本地图片"，系统会自动上传到文件服务器并生成外网访问链接
- **网络图片**: 点击"🌐 使用图片URL"，直接输入图片的网络地址

### 3. 输入问题
在"问题输入"区域输入你想要询问AI的问题，例如：
- "这个图片是在说什么？"
- "图片中有什么物体？"
- "描述一下图片中的场景"
- "图片中有几个人？"
- "这是什么品牌的产品？"

### 4. 开始分析
点击"🔍 开始分析"按钮，系统会：
- 将图片URL发送给AI
- 显示分析进度
- 返回详细的分析结果

### 5. 查看结果
分析结果显示在右侧，包括：
- 🤖 AI的智能分析
- ❓ 原始提问内容
- 🖼️ 图片访问链接
- 📊 完整API响应

## 注意事项

1. **网络连接**: 需要稳定的网络连接来访问API
2. **图片格式**: 支持常见的图片格式（JPG, PNG, GIF, BMP等）
3. **图片URL**: 使用网络图片时，确保URL可以公开访问
4. **本地图片**: 目前本地图片需要先上传到图床获取URL，或直接使用网络图片URL功能

## API信息

- **接口地址**: https://api.lolimi.cn/API/AI/gemini.php
- **请求方式**: GET
- **返回格式**: JSON

## 界面截图

应用界面包含：
- 左侧：图片选择和问题输入区域
- 右侧：分析结果显示区域
- 底部：状态栏显示当前操作状态

## 故障排除

1. **无法加载图片**: 检查图片路径或URL是否正确
2. **API调用失败**: 检查网络连接，确认API服务正常
3. **依赖包错误**: 确保已正确安装所有依赖包

## 技术栈

- **GUI框架**: tkinter
- **图片处理**: Pillow (PIL)
- **网络请求**: requests
- **多线程**: threading (避免界面冻结)

## 开发者

基于Gemini API开发的图像识别GUI应用程序。

---

如有问题或建议，欢迎反馈！
