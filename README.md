# 图像识别AI GUI应用

基于Gemini API的图像识别桌面应用程序，提供友好的图形界面来分析图片内容。

## 功能特点

- 🖼️ **图片支持**: 支持本地图片选择和网络图片URL
- 💬 **智能对话**: 可以向AI提问关于图片的任何问题
- 🎨 **现代界面**: 简洁美观的GUI设计
- ⚡ **实时预览**: 图片选择后立即预览
- 📊 **详细结果**: 显示完整的API响应和分析结果

## 安装要求

- Python 3.7+
- tkinter (通常随Python安装)
- requests
- Pillow (PIL)

## 安装步骤

1. 克隆或下载项目文件
2. 安装依赖包：
```bash
pip install -r requirements.txt
```

3. 运行应用：
```bash
python image_ai_gui.py
```

## 使用说明

### 1. 选择图片
- **本地图片**: 点击"📁 选择本地图片"按钮，选择本地图片文件
- **网络图片**: 点击"🌐 使用图片URL"按钮，输入图片的网络地址

### 2. 输入问题
在"问题输入"区域输入你想要询问AI的问题，例如：
- "这个图片是在说什么？"
- "图片中有什么物体？"
- "描述一下图片中的场景"
- "图片中有几个人？"

### 3. 开始分析
点击"🔍 开始分析"按钮，AI将分析图片并回答你的问题。

### 4. 查看结果
分析结果将显示在右侧的"分析结果"区域，包括：
- AI的分析回答
- 原始问题
- 图片链接（如果有）
- 完整的API响应数据

## 注意事项

1. **网络连接**: 需要稳定的网络连接来访问API
2. **图片格式**: 支持常见的图片格式（JPG, PNG, GIF, BMP等）
3. **图片URL**: 使用网络图片时，确保URL可以公开访问
4. **本地图片**: 目前本地图片需要先上传到图床获取URL，或直接使用网络图片URL功能

## API信息

- **接口地址**: https://api.lolimi.cn/API/AI/gemini.php
- **请求方式**: GET
- **返回格式**: JSON

## 界面截图

应用界面包含：
- 左侧：图片选择和问题输入区域
- 右侧：分析结果显示区域
- 底部：状态栏显示当前操作状态

## 故障排除

1. **无法加载图片**: 检查图片路径或URL是否正确
2. **API调用失败**: 检查网络连接，确认API服务正常
3. **依赖包错误**: 确保已正确安装所有依赖包

## 技术栈

- **GUI框架**: tkinter
- **图片处理**: Pillow (PIL)
- **网络请求**: requests
- **多线程**: threading (避免界面冻结)

## 开发者

基于Gemini API开发的图像识别GUI应用程序。

---

如有问题或建议，欢迎反馈！
