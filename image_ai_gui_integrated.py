import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import requests
import json
from PIL import Image, ImageTk
import threading
import urllib.parse
import os
from io import BytesIO
import shutil
import http.server
import socketserver
import socket
from pathlib import Path
import uuid
import time

# 导入配置
try:
    from config import SERVER_CONFIG, API_CONFIG, GUI_CONFIG
except ImportError:
    # 如果没有配置文件，使用默认配置
    SERVER_CONFIG = {
        'port': 5055,
        'images_directory': 'images',
        'external_url': 'http://kewk.hppro1.hpnu.cn',
    }
    API_CONFIG = {
        'base_url': 'https://api.lolimi.cn/API/AI/gemini.php',
        'timeout': 30,
        'default_question': '这个图片是在说什么？',
    }
    GUI_CONFIG = {
        'window_size': '950x800',
        'preview_size': (350, 250),
        'supported_formats': [
            ('图片文件', '*.jpg *.jpeg *.png *.gif *.bmp *.webp'),
            ('所有文件', '*.*')
        ],
    }

class FileServer:
    """简单的文件服务器类"""
    
    def __init__(self, port=5055, directory="images"):
        self.port = port
        self.directory = Path(directory)
        self.directory.mkdir(exist_ok=True)
        self.server = None
        self.server_thread = None
    
    def start(self):
        """启动文件服务器"""
        try:
            # 确保目录存在
            self.directory.mkdir(parents=True, exist_ok=True)
            print(f"图片目录: {self.directory.absolute()}")

            # 检查端口是否可用
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                result = s.connect_ex(('localhost', self.port))
                if result == 0:
                    print(f"端口 {self.port} 已被占用")
                    return False

            # 创建自定义HTTP处理器
            class CustomHandler(http.server.SimpleHTTPRequestHandler):
                def __init__(self, *args, directory=None, **kwargs):
                    self.directory = directory
                    super().__init__(*args, **kwargs)

                def translate_path(self, path):
                    # 将URL路径转换为文件系统路径
                    path = path.split('?', 1)[0]
                    path = path.split('#', 1)[0]
                    path = urllib.parse.unquote(path)
                    path = path.lstrip('/')

                    # 返回相对于指定目录的路径
                    return str(Path(self.directory) / path)

            # 创建处理器工厂
            def handler_factory(*args, **kwargs):
                return CustomHandler(*args, directory=str(self.directory.absolute()), **kwargs)

            # 创建HTTP服务器
            self.server = socketserver.TCPServer(("", self.port), handler_factory)

            # 在后台线程启动服务器
            self.server_thread = threading.Thread(target=self.server.serve_forever)
            self.server_thread.daemon = True
            self.server_thread.start()

            print(f"文件服务器已启动在端口 {self.port}")
            return True

        except Exception as e:
            print(f"启动文件服务器失败: {e}")
            return False
    
    def stop(self):
        """停止文件服务器"""
        if self.server:
            self.server.shutdown()
            self.server.server_close()
            print("文件服务器已停止")
    
    def add_image(self, image_path):
        """添加图片到服务器并返回相对路径"""
        try:
            image_path = Path(image_path)
            if not image_path.exists():
                print(f"源图片文件不存在: {image_path}")
                return None

            # 确保目标目录存在
            self.directory.mkdir(parents=True, exist_ok=True)

            # 生成唯一文件名
            file_extension = image_path.suffix.lower()
            if not file_extension:
                file_extension = '.jpg'  # 默认扩展名
            unique_filename = f"{uuid.uuid4().hex}{file_extension}"

            # 复制文件到服务器目录
            dest_path = self.directory / unique_filename
            print(f"复制图片: {image_path} -> {dest_path}")
            shutil.copy2(str(image_path), str(dest_path))

            # 验证文件是否复制成功
            if dest_path.exists():
                print(f"图片复制成功: {unique_filename}")
                return unique_filename
            else:
                print("图片复制失败: 目标文件不存在")
                return None

        except Exception as e:
            print(f"添加图片失败: {e}")
            import traceback
            traceback.print_exc()
            return None

class ImageAIGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("图像识别AI - 集成文件服务器")
        self.root.geometry(GUI_CONFIG['window_size'])
        self.root.configure(bg='#f0f0f0')

        # 从配置文件加载设置
        self.server_port = SERVER_CONFIG['port']
        self.external_url = SERVER_CONFIG['external_url']
        self.images_directory = SERVER_CONFIG['images_directory']

        # 创建文件服务器
        self.file_server = FileServer(port=self.server_port, directory=self.images_directory)
        
        # 设置样式
        self.setup_styles()
        
        # 创建主框架
        self.create_widgets()
        
        # 存储当前图片信息
        self.current_image_path = None
        self.current_image_url = None
        
        # 启动文件服务器
        self.start_file_server()
        
        # 绑定窗口关闭事件
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def start_file_server(self):
        """启动文件服务器"""
        if self.file_server.start():
            self.update_server_status("✅ 文件服务器运行中", "green")
        else:
            self.update_server_status("❌ 文件服务器启动失败", "red")
    
    def update_server_status(self, text, color):
        """更新服务器状态显示"""
        if hasattr(self, 'server_status_label'):
            self.server_status_label.configure(text=text, foreground=color)
    
    def on_closing(self):
        """窗口关闭时的处理"""
        if self.file_server:
            self.file_server.stop()
        self.root.destroy()
    
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        style.configure('Action.TButton', 
                       font=('Arial', 10, 'bold'),
                       padding=(10, 5))
        
        style.configure('Title.TLabel',
                       font=('Arial', 14, 'bold'),
                       background='#f0f0f0')
        
        style.configure('Subtitle.TLabel',
                       font=('Arial', 10),
                       background='#f0f0f0')
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题区域
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=10)
        
        title_label = ttk.Label(title_frame, text="图像识别AI助手", style='Title.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="基于Gemini API + 内网穿透文件服务", style='Subtitle.TLabel')
        subtitle_label.pack()
        
        # 服务器状态
        status_frame = tk.Frame(self.root, bg='#f0f0f0')
        status_frame.pack(pady=5)
        
        tk.Label(status_frame, text="服务器状态:", bg='#f0f0f0', font=('Arial', 9)).pack(side=tk.LEFT)
        self.server_status_label = tk.Label(status_frame, text="启动中...", bg='#f0f0f0', 
                                          font=('Arial', 9, 'bold'), fg='orange')
        self.server_status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 配置信息
        config_frame = tk.Frame(self.root, bg='#f0f0f0')
        config_frame.pack(pady=5)
        
        config_text = f"本地端口: {self.server_port} | 外网地址: {self.external_url}"
        tk.Label(config_frame, text=config_text, bg='#f0f0f0', font=('Arial', 8), fg='gray').pack()
        
        # 主容器
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 左侧面板
        left_frame = tk.Frame(main_frame, bg='#f0f0f0')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 图片选择区域
        image_frame = tk.LabelFrame(left_frame, text="图片选择", font=('Arial', 10, 'bold'), bg='#f0f0f0')
        image_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 按钮区域
        button_frame = tk.Frame(image_frame, bg='#f0f0f0')
        button_frame.pack(pady=10)
        
        self.upload_btn = ttk.Button(button_frame, text="📁 选择本地图片", 
                                   command=self.upload_local_image, style='Action.TButton')
        self.upload_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.url_btn = ttk.Button(button_frame, text="🌐 使用图片URL", 
                                command=self.use_image_url, style='Action.TButton')
        self.url_btn.pack(side=tk.LEFT)
        
        # 图片预览
        self.image_label = tk.Label(image_frame, text="暂无图片", 
                                  width=45, height=18, bg='white', 
                                  relief=tk.SUNKEN, bd=2)
        self.image_label.pack(pady=10)
        
        # 图片信息显示
        self.image_info_label = tk.Label(image_frame, text="", bg='#f0f0f0', 
                                       font=('Arial', 8), fg='blue', wraplength=400)
        self.image_info_label.pack(pady=5)
        
        # 问题输入区域
        question_frame = tk.LabelFrame(left_frame, text="问题输入", font=('Arial', 10, 'bold'), bg='#f0f0f0')
        question_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.question_text = scrolledtext.ScrolledText(question_frame, height=4, 
                                                     font=('Arial', 10), wrap=tk.WORD)
        self.question_text.pack(fill=tk.X, padx=10, pady=10)
        self.question_text.insert('1.0', API_CONFIG['default_question'])
        
        # 分析按钮
        self.analyze_btn = ttk.Button(left_frame, text="🔍 开始分析", 
                                    command=self.analyze_image, style='Action.TButton')
        self.analyze_btn.pack(pady=10)
        
        # 右侧面板
        right_frame = tk.Frame(main_frame, bg='#f0f0f0')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 结果显示区域
        result_frame = tk.LabelFrame(right_frame, text="分析结果", font=('Arial', 10, 'bold'), bg='#f0f0f0')
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, font=('Arial', 10), 
                                                   wrap=tk.WORD, state=tk.DISABLED)
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = tk.Label(self.root, textvariable=self.status_var,
                            relief=tk.SUNKEN, anchor=tk.W, bg='#e0e0e0')
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)

    def upload_local_image(self):
        """上传本地图片"""
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=GUI_CONFIG['supported_formats']
        )

        if file_path:
            self.status_var.set("正在处理本地图片...")

            # 在后台线程处理图片上传
            thread = threading.Thread(target=self._process_local_image, args=(file_path,))
            thread.daemon = True
            thread.start()

    def _process_local_image(self, file_path):
        """在后台线程处理本地图片"""
        try:
            # 添加图片到文件服务器
            filename = self.file_server.add_image(file_path)
            if filename:
                # 构建完整的外网URL
                self.current_image_url = f"{self.external_url}/{filename}"
                self.current_image_path = file_path

                # 在主线程更新UI
                self.root.after(0, self._update_image_display, file_path, self.current_image_url)
            else:
                self.root.after(0, self._show_error, "图片上传失败")

        except Exception as e:
            self.root.after(0, self._show_error, f"处理图片失败: {str(e)}")

    def _update_image_display(self, local_path, external_url):
        """更新图片显示"""
        try:
            # 显示本地图片预览
            self.display_image(local_path)

            # 更新图片信息
            filename = os.path.basename(local_path)
            info_text = f"本地文件: {filename}\n外网地址: {external_url}"
            self.image_info_label.configure(text=info_text)

            self.status_var.set(f"图片已上传: {filename}")

        except Exception as e:
            self._show_error(f"更新显示失败: {str(e)}")

    def use_image_url(self):
        """使用图片URL"""
        import tkinter.simpledialog
        url = tkinter.simpledialog.askstring("图片URL", "请输入图片URL:")
        if url:
            self.current_image_url = url
            self.current_image_path = None
            try:
                self.display_image_from_url(url)
                self.image_info_label.configure(text=f"网络图片: {url}")
                self.status_var.set("已加载网络图片")
            except Exception as e:
                messagebox.showerror("错误", f"无法加载图片URL: {str(e)}")

    def display_image(self, image_path):
        """显示本地图片"""
        try:
            image = Image.open(image_path)
            # 保持宽高比缩放
            image.thumbnail(GUI_CONFIG['preview_size'], Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(image)
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用

        except Exception as e:
            raise Exception(f"显示图片失败: {str(e)}")

    def display_image_from_url(self, url):
        """从URL显示图片"""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()

            image = Image.open(BytesIO(response.content))
            image.thumbnail(GUI_CONFIG['preview_size'], Image.Resampling.LANCZOS)

            photo = ImageTk.PhotoImage(image)
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo

        except Exception as e:
            raise Exception(f"加载网络图片失败: {str(e)}")

    def analyze_image(self):
        """分析图片"""
        question = self.question_text.get('1.0', tk.END).strip()
        if not question:
            messagebox.showwarning("警告", "请输入问题内容")
            return

        if not self.current_image_url:
            messagebox.showwarning("警告", "请先选择图片")
            return

        # 禁用按钮，显示进度
        self.analyze_btn.configure(state='disabled', text='分析中...')
        self.status_var.set("正在调用AI分析...")

        # 在后台线程调用API
        thread = threading.Thread(target=self._call_api, args=(question, self.current_image_url))
        thread.daemon = True
        thread.start()

    def _call_api(self, question, image_url):
        """调用API"""
        try:
            base_url = API_CONFIG['base_url']
            params = {
                'msg': question,
                'img': image_url
            }

            response = requests.get(base_url, params=params, timeout=API_CONFIG['timeout'])
            response.raise_for_status()

            result = response.json()
            self.root.after(0, self._update_result, result)

        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求错误: {str(e)}"
            self.root.after(0, self._show_error, error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"响应解析错误: {str(e)}"
            self.root.after(0, self._show_error, error_msg)
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            self.root.after(0, self._show_error, error_msg)

    def _update_result(self, result):
        """更新结果显示"""
        try:
            self.result_text.configure(state=tk.NORMAL)
            self.result_text.delete('1.0', tk.END)

            if result.get('code') == 200:
                data = result.get('data', {})

                output_text = f"🤖 AI分析结果:\n{data.get('output', '无结果')}\n\n"
                output_text += f"❓ 提问内容:\n{data.get('content', '无问题')}\n\n"

                if data.get('image') and data.get('image') != '没有图片':
                    output_text += f"🖼️ 图片链接:\n{data.get('image', '')}\n\n"

                output_text += f"📊 API响应详情:\n{json.dumps(result, ensure_ascii=False, indent=2)}"

                self.result_text.insert('1.0', output_text)
                self.status_var.set("✅ 分析完成")

            else:
                error_text = f"❌ API返回错误:\n状态码: {result.get('code', '未知')}\n"
                error_text += f"详细信息: {json.dumps(result, ensure_ascii=False, indent=2)}"
                self.result_text.insert('1.0', error_text)
                self.status_var.set("❌ 分析失败")

        except Exception as e:
            self.result_text.insert('1.0', f"❌ 结果处理错误: {str(e)}")
            self.status_var.set("❌ 处理错误")
        finally:
            self.result_text.configure(state=tk.DISABLED)
            self.analyze_btn.configure(state='normal', text='🔍 开始分析')

    def _show_error(self, error_msg):
        """显示错误信息"""
        self.result_text.configure(state=tk.NORMAL)
        self.result_text.delete('1.0', tk.END)
        self.result_text.insert('1.0', f"❌ 错误: {error_msg}")
        self.result_text.configure(state=tk.DISABLED)

        self.status_var.set("❌ 发生错误")
        self.analyze_btn.configure(state='normal', text='🔍 开始分析')

        messagebox.showerror("错误", error_msg)


def main():
    """主函数"""
    root = tk.Tk()

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # 创建应用
    app = ImageAIGUI(root)

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    # 显示启动信息
    print("=" * 60)
    print("图像识别AI GUI应用 - 集成版")
    print("=" * 60)
    print(f"本地文件服务器端口: {app.server_port}")
    print(f"外网访问地址: {app.external_url}")
    print("请确保内网穿透服务正在运行!")
    print("=" * 60)

    root.mainloop()


if __name__ == "__main__":
    main()
