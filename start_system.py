#!/usr/bin/env python3
"""
图像识别AI系统启动脚本
包含文件服务器和GUI界面的一键启动
"""

import sys
import subprocess
import importlib.util
import os
import time
import threading
from pathlib import Path

def check_dependencies():
    """检查依赖包"""
    required_packages = {
        'requests': 'requests',
        'PIL': 'Pillow'
    }
    
    missing_packages = []
    
    for package_name, pip_name in required_packages.items():
        if importlib.util.find_spec(package_name) is None:
            missing_packages.append(pip_name)
    
    if missing_packages:
        print("❌ 检测到缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        
        response = input("\n是否自动安装这些依赖包? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            try:
                for package in missing_packages:
                    print(f"正在安装 {package}...")
                    subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print("✅ 所有依赖包安装完成!")
                return True
            except subprocess.CalledProcessError as e:
                print(f"❌ 安装失败: {e}")
                return False
        else:
            print("请手动安装依赖包: pip install -r requirements.txt")
            return False
    
    print("✅ 所有依赖包已安装")
    return True

def check_files():
    """检查必要文件"""
    required_files = [
        'image_ai_gui_integrated.py',
        'file_server.py'
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
    
    if missing_files:
        print("❌ 缺少以下必要文件:")
        for file in missing_files:
            print(f"   - {file}")
        return False
    
    print("✅ 所有必要文件存在")
    return True

def show_system_info():
    """显示系统信息"""
    print("\n" + "=" * 60)
    print("🚀 图像识别AI系统")
    print("=" * 60)
    print("功能特点:")
    print("  📁 本地图片自动上传到文件服务器")
    print("  🌐 支持网络图片URL直接使用")
    print("  🤖 基于Gemini API的智能图像分析")
    print("  🔗 集成内网穿透，外网可访问")
    print("\n配置信息:")
    print("  本地端口: 5055")
    print("  外网地址: http://kewk.hppro1.hpnu.cn")
    print("  图片目录: ./images/")
    print("\n注意事项:")
    print("  ⚠️  请确保内网穿透服务正在运行")
    print("  ⚠️  确保端口5055未被其他程序占用")
    print("  ⚠️  网络连接正常，可访问API服务")
    print("=" * 60)

def start_gui():
    """启动GUI应用"""
    try:
        print("\n🎯 正在启动GUI应用...")
        from image_ai_gui_integrated import main as gui_main
        gui_main()
    except ImportError as e:
        print(f"❌ 导入GUI模块失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("🔧 正在初始化系统...")
    
    # 检查依赖
    if not check_dependencies():
        input("\n按回车键退出...")
        return
    
    # 检查文件
    if not check_files():
        input("\n按回车键退出...")
        return
    
    # 显示系统信息
    show_system_info()
    
    # 询问是否继续
    response = input("\n是否启动系统? (y/n): ").lower().strip()
    if response not in ['y', 'yes', '是']:
        print("已取消启动")
        return
    
    # 创建图片目录
    images_dir = Path("images")
    images_dir.mkdir(exist_ok=True)
    print(f"✅ 图片目录已准备: {images_dir.absolute()}")
    
    # 启动GUI
    if start_gui():
        print("✅ 系统启动成功!")
    else:
        print("❌ 系统启动失败!")
        input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，正在退出...")
    except Exception as e:
        print(f"\n❌ 系统错误: {e}")
        input("\n按回车键退出...")
