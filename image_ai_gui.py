import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import requests
import json
from PIL import Image, ImageTk
import threading
import urllib.parse
import os
from io import BytesIO
import base64
import tempfile
import webbrowser

class ImageAIGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("图像识别AI - Gemini API")
        self.root.geometry("800x700")
        self.root.configure(bg='#f0f0f0')
        
        # 设置样式
        self.setup_styles()
        
        # 创建主框架
        self.create_widgets()
        
        # 存储当前图片路径和URL
        self.current_image_path = None
        self.current_image_url = None
        
    def setup_styles(self):
        """设置界面样式"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # 配置按钮样式
        style.configure('Action.TButton', 
                       font=('Arial', 10, 'bold'),
                       padding=(10, 5))
        
        # 配置标签样式
        style.configure('Title.TLabel',
                       font=('Arial', 14, 'bold'),
                       background='#f0f0f0')
        
        style.configure('Subtitle.TLabel',
                       font=('Arial', 10),
                       background='#f0f0f0')
    
    def create_widgets(self):
        """创建界面组件"""
        # 标题
        title_frame = tk.Frame(self.root, bg='#f0f0f0')
        title_frame.pack(pady=10)
        
        title_label = ttk.Label(title_frame, text="图像识别AI助手", style='Title.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(title_frame, text="基于Gemini API的智能图像分析", style='Subtitle.TLabel')
        subtitle_label.pack()
        
        # 主容器
        main_frame = tk.Frame(self.root, bg='#f0f0f0')
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 左侧面板 - 图片和输入
        left_frame = tk.Frame(main_frame, bg='#f0f0f0')
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 图片选择区域
        image_frame = tk.LabelFrame(left_frame, text="图片选择", font=('Arial', 10, 'bold'), bg='#f0f0f0')
        image_frame.pack(fill=tk.X, pady=(0, 10))
        
        # 图片选择按钮
        button_frame = tk.Frame(image_frame, bg='#f0f0f0')
        button_frame.pack(pady=10)
        
        self.upload_btn = ttk.Button(button_frame, text="📁 选择本地图片", 
                                   command=self.upload_image, style='Action.TButton')
        self.upload_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.url_btn = ttk.Button(button_frame, text="🌐 使用图片URL", 
                                command=self.use_image_url, style='Action.TButton')
        self.url_btn.pack(side=tk.LEFT)
        
        # 图片预览区域
        self.image_label = tk.Label(image_frame, text="暂无图片", 
                                  width=40, height=15, bg='white', 
                                  relief=tk.SUNKEN, bd=2)
        self.image_label.pack(pady=10)
        
        # 问题输入区域
        question_frame = tk.LabelFrame(left_frame, text="问题输入", font=('Arial', 10, 'bold'), bg='#f0f0f0')
        question_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.question_text = scrolledtext.ScrolledText(question_frame, height=4, 
                                                     font=('Arial', 10), wrap=tk.WORD)
        self.question_text.pack(fill=tk.X, padx=10, pady=10)
        self.question_text.insert('1.0', '这个图片是在说什么？')
        
        # 分析按钮
        self.analyze_btn = ttk.Button(left_frame, text="🔍 开始分析", 
                                    command=self.analyze_image, style='Action.TButton')
        self.analyze_btn.pack(pady=10)
        
        # 右侧面板 - 结果显示
        right_frame = tk.Frame(main_frame, bg='#f0f0f0')
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)
        
        # 结果显示区域
        result_frame = tk.LabelFrame(right_frame, text="分析结果", font=('Arial', 10, 'bold'), bg='#f0f0f0')
        result_frame.pack(fill=tk.BOTH, expand=True)
        
        self.result_text = scrolledtext.ScrolledText(result_frame, font=('Arial', 10), 
                                                   wrap=tk.WORD, state=tk.DISABLED)
        self.result_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # 状态栏
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        status_bar = tk.Label(self.root, textvariable=self.status_var, 
                            relief=tk.SUNKEN, anchor=tk.W, bg='#e0e0e0')
        status_bar.pack(side=tk.BOTTOM, fill=tk.X)
    
    def upload_image(self):
        """上传本地图片"""
        file_path = filedialog.askopenfilename(
            title="选择图片文件",
            filetypes=[
                ("图片文件", "*.jpg *.jpeg *.png *.gif *.bmp"),
                ("所有文件", "*.*")
            ]
        )
        
        if file_path:
            self.current_image_path = file_path
            self.current_image_url = None
            self.display_image(file_path)
            self.status_var.set(f"已选择图片: {os.path.basename(file_path)}")
    
    def use_image_url(self):
        """使用图片URL"""
        url = tk.simpledialog.askstring("图片URL", "请输入图片URL:")
        if url:
            self.current_image_url = url
            self.current_image_path = None
            try:
                self.display_image_from_url(url)
                self.status_var.set(f"已加载图片URL")
            except Exception as e:
                messagebox.showerror("错误", f"无法加载图片URL: {str(e)}")
    
    def display_image(self, image_path):
        """显示本地图片"""
        try:
            # 打开并调整图片大小
            image = Image.open(image_path)
            image.thumbnail((300, 200), Image.Resampling.LANCZOS)
            
            # 转换为tkinter可用的格式
            photo = ImageTk.PhotoImage(image)
            
            # 更新标签
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用
            
        except Exception as e:
            messagebox.showerror("错误", f"无法显示图片: {str(e)}")
    
    def display_image_from_url(self, url):
        """从URL显示图片"""
        try:
            response = requests.get(url, timeout=10)
            response.raise_for_status()
            
            # 从响应内容创建图片
            image = Image.open(BytesIO(response.content))
            image.thumbnail((300, 200), Image.Resampling.LANCZOS)
            
            # 转换为tkinter可用的格式
            photo = ImageTk.PhotoImage(image)
            
            # 更新标签
            self.image_label.configure(image=photo, text="")
            self.image_label.image = photo  # 保持引用
            
        except Exception as e:
            raise Exception(f"加载图片失败: {str(e)}")

    def analyze_image(self):
        """分析图片"""
        # 获取问题文本
        question = self.question_text.get('1.0', tk.END).strip()
        if not question:
            messagebox.showwarning("警告", "请输入问题内容")
            return

        # 检查是否有图片
        image_url = ""
        if self.current_image_url:
            image_url = self.current_image_url
        elif self.current_image_path:
            # 对于本地图片，我们需要先上传到图床或使用base64编码
            # 这里简化处理，提示用户使用URL
            messagebox.showinfo("提示", "本地图片需要先上传到图床获取URL，或直接使用图片URL功能")
            return

        # 在新线程中执行API调用
        self.analyze_btn.configure(state='disabled', text='分析中...')
        self.status_var.set("正在分析图片...")

        thread = threading.Thread(target=self._call_api, args=(question, image_url))
        thread.daemon = True
        thread.start()

    def _call_api(self, question, image_url):
        """调用API的线程函数"""
        try:
            # 构建API请求URL
            base_url = "https://api.lolimi.cn/API/AI/gemini.php"
            params = {
                'msg': question,
                'img': image_url
            }

            # 发送请求
            response = requests.get(base_url, params=params, timeout=30)
            response.raise_for_status()

            # 解析响应
            result = response.json()

            # 在主线程中更新UI
            self.root.after(0, self._update_result, result)

        except requests.exceptions.RequestException as e:
            error_msg = f"网络请求错误: {str(e)}"
            self.root.after(0, self._show_error, error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"响应解析错误: {str(e)}"
            self.root.after(0, self._show_error, error_msg)
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"
            self.root.after(0, self._show_error, error_msg)

    def _update_result(self, result):
        """更新结果显示"""
        try:
            # 启用文本框
            self.result_text.configure(state=tk.NORMAL)
            self.result_text.delete('1.0', tk.END)

            # 格式化显示结果
            if result.get('code') == 200:
                data = result.get('data', {})

                # 显示分析结果
                output_text = f"📝 分析结果:\n{data.get('output', '无结果')}\n\n"
                output_text += f"❓ 原始问题:\n{data.get('content', '无问题')}\n\n"

                if data.get('image') and data.get('image') != '没有图片':
                    output_text += f"🖼️ 图片链接:\n{data.get('image', '')}\n\n"

                # 显示完整响应（调试用）
                output_text += f"📊 完整响应:\n{json.dumps(result, ensure_ascii=False, indent=2)}"

                self.result_text.insert('1.0', output_text)
                self.status_var.set("分析完成")

            else:
                error_text = f"❌ API返回错误:\n状态码: {result.get('code', '未知')}\n"
                error_text += f"响应内容: {json.dumps(result, ensure_ascii=False, indent=2)}"
                self.result_text.insert('1.0', error_text)
                self.status_var.set("分析失败")

        except Exception as e:
            self.result_text.insert('1.0', f"❌ 结果处理错误: {str(e)}")
            self.status_var.set("处理错误")
        finally:
            # 禁用文本框编辑
            self.result_text.configure(state=tk.DISABLED)
            # 恢复按钮状态
            self.analyze_btn.configure(state='normal', text='🔍 开始分析')

    def _show_error(self, error_msg):
        """显示错误信息"""
        self.result_text.configure(state=tk.NORMAL)
        self.result_text.delete('1.0', tk.END)
        self.result_text.insert('1.0', f"❌ 错误: {error_msg}")
        self.result_text.configure(state=tk.DISABLED)

        self.status_var.set("发生错误")
        self.analyze_btn.configure(state='normal', text='🔍 开始分析')

        messagebox.showerror("错误", error_msg)


def main():
    """主函数"""
    root = tk.Tk()

    # 导入simpledialog
    import tkinter.simpledialog
    tk.simpledialog = tkinter.simpledialog

    app = ImageAIGUI(root)

    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap('icon.ico')
    except:
        pass

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    root.mainloop()


if __name__ == "__main__":
    main()
